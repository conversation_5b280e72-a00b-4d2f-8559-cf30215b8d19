/**
 * Production-Grade Global Error Handler
 * Provides comprehensive error handling, classification, logging, and debugging capabilities
 */

import fs from 'fs/promises';
import path from 'path';

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
};

// Error categories for better classification
export const ERROR_CATEGORIES = {
  NETWORK: 'network',
  AUTH: 'authentication',
  VALIDATION: 'validation',
  BROWSER: 'browser',
  AI_SERVICE: 'ai_service',
  FILE_SYSTEM: 'file_system',
  TIMEOUT: 'timeout',
  RATE_LIMIT: 'rate_limit',
  SYSTEM: 'system',
  UNKNOWN: 'unknown',
};

// Retry strategies
export const RETRY_STRATEGIES = {
  NONE: 'none',
  LINEAR: 'linear',
  EXPONENTIAL: 'exponential',
  CUSTOM: 'custom',
};

/**
 * Enhanced Error class with rich metadata
 */
export class EnhancedError extends Error {
  constructor(message, options = {}) {
    super(message);
    this.name = 'EnhancedError';
    this.timestamp = new Date().toISOString();
    this.category = options.category || ERROR_CATEGORIES.UNKNOWN;
    this.severity = options.severity || ERROR_SEVERITY.MEDIUM;
    this.context = options.context || {};
    this.originalError = options.originalError || null;
    this.shouldRetry =
      options.shouldRetry !== undefined ? options.shouldRetry : false;
    this.retryStrategy = options.retryStrategy || RETRY_STRATEGIES.NONE;
    this.maxRetries = options.maxRetries || 0;
    this.userMessage = options.userMessage || message;
    this.debugInfo = this._generateDebugInfo(options);
    this.stackId = this._generateStackId();
  }

  _generateDebugInfo(options) {
    return {
      nodeVersion: process.version,
      platform: process.platform,
      architecture: process.arch,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      pid: process.pid,
      cwd: process.cwd(),
      env: process.env.NODE_ENV || 'development',
      customData: options.debugData || {},
    };
  }

  _generateStackId() {
    // Generate a unique ID for this error occurrence
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `${this.category}_${timestamp}_${random}`;
  }

  /**
   * Get a formatted error report
   */
  getReport() {
    return {
      id: this.stackId,
      timestamp: this.timestamp,
      message: this.message,
      userMessage: this.userMessage,
      category: this.category,
      severity: this.severity,
      shouldRetry: this.shouldRetry,
      retryStrategy: this.retryStrategy,
      maxRetries: this.maxRetries,
      context: this.context,
      debugInfo: this.debugInfo,
      stack: this.stack,
      originalError: this.originalError
        ? {
            name: this.originalError.name,
            message: this.originalError.message,
            stack: this.originalError.stack,
          }
        : null,
    };
  }
}

/**
 * Global Error Handler Class
 */
export class GlobalErrorHandler {
  constructor(options = {}) {
    this.logLevel = options.logLevel || 'info';
    this.enableFileLogging = options.enableFileLogging !== false;
    this.logDirectory = options.logDirectory || './logs';
    this.enableConsoleColors = options.enableConsoleColors !== false;
    this.maxLogFileSize = options.maxLogFileSize || 10 * 1024 * 1024; // 10MB
    this.errorStats = new Map();

    // Initialize log directory if file logging is enabled
    if (this.enableFileLogging) {
      this._initializeLogDirectory();
    }
  }

  async _initializeLogDirectory() {
    try {
      await fs.mkdir(this.logDirectory, { recursive: true });
    } catch (error) {
      console.warn(`Failed to create log directory: ${error.message}`);
      this.enableFileLogging = false;
    }
  }

  /**
   * Classify error based on various criteria
   */
  classifyError(error) {
    const errorString = error.toString().toLowerCase();
    const statusCode = error.statusCode || error.status || 0;

    // Network errors
    if (
      errorString.includes('network') ||
      errorString.includes('timeout') ||
      errorString.includes('connection') ||
      errorString.includes('enotfound') ||
      errorString.includes('econnrefused') ||
      errorString.includes('socket')
    ) {
      return {
        category: ERROR_CATEGORIES.NETWORK,
        severity: ERROR_SEVERITY.MEDIUM,
        shouldRetry: true,
        retryStrategy: RETRY_STRATEGIES.EXPONENTIAL,
        maxRetries: 3,
        userMessage: 'Network connectivity issue detected. Retrying...',
      };
    }

    // Authentication errors
    if (
      errorString.includes('api key') ||
      errorString.includes('unauthorized') ||
      errorString.includes('authentication') ||
      errorString.includes('forbidden') ||
      statusCode === 401 ||
      statusCode === 403
    ) {
      return {
        category: ERROR_CATEGORIES.AUTH,
        severity: ERROR_SEVERITY.HIGH,
        shouldRetry: false,
        userMessage:
          'Authentication failed. Please check your API credentials.',
      };
    }

    // Rate limiting / Quota errors
    if (
      errorString.includes('quota') ||
      errorString.includes('rate limit') ||
      errorString.includes('resource_exhausted') ||
      errorString.includes('too many requests') ||
      statusCode === 429
    ) {
      return {
        category: ERROR_CATEGORIES.RATE_LIMIT,
        severity: ERROR_SEVERITY.HIGH,
        shouldRetry: true,
        retryStrategy: RETRY_STRATEGIES.EXPONENTIAL,
        maxRetries: 2,
        userMessage: 'Rate limit exceeded. Please wait before retrying.',
      };
    }

    // Browser/Playwright errors
    if (
      errorString.includes('playwright') ||
      errorString.includes('browser') ||
      errorString.includes('page') ||
      errorString.includes('frame') ||
      errorString.includes('element')
    ) {
      return {
        category: ERROR_CATEGORIES.BROWSER,
        severity: ERROR_SEVERITY.MEDIUM,
        shouldRetry: true,
        retryStrategy: RETRY_STRATEGIES.LINEAR,
        maxRetries: 2,
        userMessage:
          'Browser automation error. Retrying with fresh browser instance.',
      };
    }

    // File system errors
    if (
      errorString.includes('enoent') ||
      errorString.includes('eacces') ||
      errorString.includes('file') ||
      errorString.includes('directory')
    ) {
      return {
        category: ERROR_CATEGORIES.FILE_SYSTEM,
        severity: ERROR_SEVERITY.MEDIUM,
        shouldRetry: false,
        userMessage:
          'File system access error. Please check permissions and paths.',
      };
    }

    // AI Service errors
    if (
      errorString.includes('ai') ||
      errorString.includes('gemini') ||
      errorString.includes('model') ||
      errorString.includes('generation')
    ) {
      return {
        category: ERROR_CATEGORIES.AI_SERVICE,
        severity: ERROR_SEVERITY.MEDIUM,
        shouldRetry: true,
        retryStrategy: RETRY_STRATEGIES.LINEAR,
        maxRetries: 2,
        userMessage: 'AI service error. Continuing without AI features.',
      };
    }

    // Timeout errors
    if (
      errorString.includes('timeout') ||
      errorString.includes('time out') ||
      errorString.includes('timed out')
    ) {
      return {
        category: ERROR_CATEGORIES.TIMEOUT,
        severity: ERROR_SEVERITY.MEDIUM,
        shouldRetry: true,
        retryStrategy: RETRY_STRATEGIES.LINEAR,
        maxRetries: 3,
        userMessage: 'Operation timed out. Retrying with extended timeout.',
      };
    }

    // Validation errors
    if (
      errorString.includes('validation') ||
      errorString.includes('invalid') ||
      errorString.includes('schema') ||
      statusCode === 400
    ) {
      return {
        category: ERROR_CATEGORIES.VALIDATION,
        severity: ERROR_SEVERITY.MEDIUM,
        shouldRetry: false,
        userMessage: 'Input validation failed. Please check your inputs.',
      };
    }

    // Service unavailable
    if (statusCode === 503 || errorString.includes('service unavailable')) {
      return {
        category: ERROR_CATEGORIES.AI_SERVICE,
        severity: ERROR_SEVERITY.HIGH,
        shouldRetry: true,
        retryStrategy: RETRY_STRATEGIES.EXPONENTIAL,
        maxRetries: 3,
        userMessage: 'Service temporarily unavailable. Retrying...',
      };
    }

    // Default classification
    return {
      category: ERROR_CATEGORIES.UNKNOWN,
      severity: ERROR_SEVERITY.MEDIUM,
      shouldRetry: false,
      userMessage: 'An unexpected error occurred. Please try again.',
    };
  }

  /**
   * Handle error with comprehensive processing
   */
  async handleError(error, context = {}) {
    const classification = this.classifyError(error);

    const enhancedError = new EnhancedError(error.message, {
      category: classification.category,
      severity: classification.severity,
      shouldRetry: classification.shouldRetry,
      retryStrategy: classification.retryStrategy,
      maxRetries: classification.maxRetries,
      userMessage: classification.userMessage,
      context,
      originalError: error,
      debugData: {
        classification,
        handledAt: new Date().toISOString(),
      },
    });

    // Update error statistics
    this._updateErrorStats(enhancedError);

    // Log the error
    await this._logError(enhancedError);

    return enhancedError;
  }

  /**
   * Update error statistics
   */
  _updateErrorStats(error) {
    const key = `${error.category}_${error.severity}`;
    const current = this.errorStats.get(key) || {
      count: 0,
      lastOccurrence: null,
    };
    this.errorStats.set(key, {
      count: current.count + 1,
      lastOccurrence: error.timestamp,
      category: error.category,
      severity: error.severity,
    });
  }

  /**
   * Log error with appropriate formatting
   */
  async _logError(error) {
    const logEntry = {
      timestamp: error.timestamp,
      id: error.stackId,
      level: this._severityToLogLevel(error.severity),
      category: error.category,
      message: error.message,
      userMessage: error.userMessage,
      context: error.context,
      shouldRetry: error.shouldRetry,
      stack: error.stack,
    };

    // Console logging with colors
    this._logToConsole(logEntry, error);

    // File logging
    if (this.enableFileLogging) {
      await this._logToFile(logEntry, error);
    }
  }

  _severityToLogLevel(severity) {
    switch (severity) {
      case ERROR_SEVERITY.LOW:
        return 'info';
      case ERROR_SEVERITY.MEDIUM:
        return 'warn';
      case ERROR_SEVERITY.HIGH:
        return 'error';
      case ERROR_SEVERITY.CRITICAL:
        return 'error';
      default:
        return 'warn';
    }
  }

  _logToConsole(logEntry, error) {
    const emoji = this._getSeverityEmoji(error.severity);
    const color = this._getSeverityColor(error.severity);

    if (this.enableConsoleColors) {
      console.log(
        `\n${emoji} ${color}[${error.category.toUpperCase()}]${'\x1b[0m'} ${
          error.userMessage
        }`
      );
      console.log(`   ${'\x1b[90m'}ID: ${error.stackId}${'\x1b[0m'}`);
      console.log(`   ${'\x1b[90m'}Time: ${error.timestamp}${'\x1b[0m'}`);

      // Log the actual error message if it's different from user message
      if (error.message && error.message !== error.userMessage) {
        console.log(`   ${'\x1b[91m'}Error: ${error.message}${'\x1b[0m'}`);
      }

      // Log original error details if available
      if (
        error.originalError &&
        error.originalError.message &&
        error.originalError.message !== error.message
      ) {
        console.log(
          `   ${'\x1b[91m'}Original: ${error.originalError.message}${'\x1b[0m'}`
        );
      }

      if (error.shouldRetry) {
        console.log(
          `   ${'\x1b[33m'}↻ Retry: ${error.retryStrategy} (max: ${
            error.maxRetries
          })${'\x1b[0m'}`
        );
      }

      if (error.context && Object.keys(error.context).length > 0) {
        console.log(
          `   ${'\x1b[90m'}Context: ${JSON.stringify(
            error.context
          )}${'\x1b[0m'}`
        );
      }

      // Log stack trace for debugging (only first few lines to avoid clutter)
      if (error.stack) {
        const stackLines = error.stack.split('\n').slice(0, 4); // First 4 lines of stack
        console.log(
          `   ${'\x1b[90m'}Stack: ${stackLines.join('\n   ')}${'\x1b[0m'}`
        );
      }
    } else {
      console.log(
        `\n${emoji} [${error.category.toUpperCase()}] ${error.userMessage}`
      );
      console.log(`   ID: ${error.stackId}`);
      console.log(`   Time: ${error.timestamp}`);

      // Log the actual error message if it's different from user message
      if (error.message && error.message !== error.userMessage) {
        console.log(`   Error: ${error.message}`);
      }

      // Log original error details if available
      if (
        error.originalError &&
        error.originalError.message &&
        error.originalError.message !== error.message
      ) {
        console.log(`   Original: ${error.originalError.message}`);
      }

      if (error.shouldRetry) {
        console.log(
          `   Retry: ${error.retryStrategy} (max: ${error.maxRetries})`
        );
      }

      if (error.context && Object.keys(error.context).length > 0) {
        console.log(`   Context: ${JSON.stringify(error.context)}`);
      }

      // Log stack trace for debugging (only first few lines to avoid clutter)
      if (error.stack) {
        const stackLines = error.stack.split('\n').slice(0, 4); // First 4 lines of stack
        console.log(`   Stack: ${stackLines.join('\n   ')}`);
      }
    }
  }

  async _logToFile(logEntry, error) {
    try {
      const logFileName = `error-${new Date().toISOString().split('T')[0]}.log`;
      const logFilePath = path.join(this.logDirectory, logFileName);

      const logLine = `${JSON.stringify({
        ...logEntry,
        fullReport: error.getReport(),
      })}\n`;

      await fs.appendFile(logFilePath, logLine);

      // Check file size and rotate if necessary
      await this._rotateLogFileIfNeeded(logFilePath);
    } catch (error) {
      console.warn(`Failed to write to log file: ${error.message}`);
    }
  }

  async _rotateLogFileIfNeeded(logFilePath) {
    try {
      const stats = await fs.stat(logFilePath);
      if (stats.size > this.maxLogFileSize) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedPath = logFilePath.replace('.log', `-${timestamp}.log`);
        await fs.rename(logFilePath, rotatedPath);
      }
    } catch (error) {
      console.warn(`Failed to rotate log file: ${error.message}`);
    }
  }

  _getSeverityEmoji(severity) {
    switch (severity) {
      case ERROR_SEVERITY.LOW:
        return 'ℹ️';
      case ERROR_SEVERITY.MEDIUM:
        return '⚠️';
      case ERROR_SEVERITY.HIGH:
        return '❌';
      case ERROR_SEVERITY.CRITICAL:
        return '🚨';
      default:
        return '⚠️';
    }
  }

  _getSeverityColor(severity) {
    switch (severity) {
      case ERROR_SEVERITY.LOW:
        return '\x1b[36m'; // cyan
      case ERROR_SEVERITY.MEDIUM:
        return '\x1b[33m'; // yellow
      case ERROR_SEVERITY.HIGH:
        return '\x1b[31m'; // red
      case ERROR_SEVERITY.CRITICAL:
        return '\x1b[35m'; // magenta
      default:
        return '\x1b[33m'; // yellow
    }
  }

  /**
   * Get error statistics summary
   */
  getErrorStats() {
    const stats = Array.from(this.errorStats.entries()).map(([key, value]) => ({
      key,
      ...value,
    }));

    return {
      totalErrors: stats.reduce((sum, stat) => sum + stat.count, 0),
      errorsByCategory: stats.reduce((acc, stat) => {
        acc[stat.category] = (acc[stat.category] || 0) + stat.count;
        return acc;
      }, {}),
      errorsBySeverity: stats.reduce((acc, stat) => {
        acc[stat.severity] = (acc[stat.severity] || 0) + stat.count;
        return acc;
      }, {}),
      details: stats,
    };
  }

  /**
   * Reset error statistics
   */
  resetStats() {
    this.errorStats.clear();
  }
}

// Create a global instance
export const globalErrorHandler = new GlobalErrorHandler({
  enableConsoleColors: true,
  enableFileLogging: false, // Disabled for production - console logging only
  logDirectory: './logs/errors',
});

/**
 * Convenience function for handling errors
 */
export async function handleError(error, context = {}) {
  return await globalErrorHandler.handleError(error, context);
}

/**
 * Convenience function for creating enhanced errors
 */
export function createError(message, options = {}) {
  return new EnhancedError(message, options);
}

/**
 * Convenience function to check if error should be retried
 */
export function shouldRetryError(error) {
  if (error instanceof EnhancedError) {
    return error.shouldRetry;
  }

  const classification = globalErrorHandler.classifyError(error);
  return classification.shouldRetry;
}

/**
 * Get retry configuration for an error
 */
export function getRetryConfig(error) {
  if (error instanceof EnhancedError) {
    return {
      shouldRetry: error.shouldRetry,
      strategy: error.retryStrategy,
      maxRetries: error.maxRetries,
    };
  }

  const classification = globalErrorHandler.classifyError(error);
  return {
    shouldRetry: classification.shouldRetry,
    strategy: classification.retryStrategy,
    maxRetries: classification.maxRetries,
  };
}
